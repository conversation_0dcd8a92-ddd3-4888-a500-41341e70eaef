'use strict';
const { Model, DataTypes } = require('sequelize');
const { sequelize } = require('../config/sequelize');

/**
 * AppModel - Base model class that handles Sequelize complexity
 * Similar to Rails ActiveRecord, child classes only need to define:
 * - static schema() - returns the model attributes
 * - static associate(models) - defines associations (optional)
 * - static hooks() - defines lifecycle hooks (optional)
 * - static options() - additional model options (optional)
 */
class AppModel extends Model {
  /**
   * Initialize a model class with schema, associations, and hooks
   * This method should be called by child classes
   */
  static initModel(modelName, childClass) {
    // Get schema from child class
    const schema = childClass.schema ? childClass.schema() : {};

    // Get hooks from child class
    const hooks = childClass.hooks ? childClass.hooks() : {};

    // Get additional options from child class
    const additionalOptions = childClass.options ? childClass.options() : {};

    // Calculate table name using Rails convention
    const tableName = modelName
      .replace(/([A-Z])/g, '_$1')
      .toLowerCase()
      .replace(/^_/, '')
      .concat('s');

    // Default options that provide Rails-like behavior
    const defaultOptions = {
      sequelize,
      modelName,
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      tableName,
      hooks: {
        // Default hook to update updated_at timestamp
        beforeUpdate: instance => {
          instance.updated_at = new Date();
        },
        // Merge with custom hooks
        ...hooks,
      },
    };

    // Merge options
    const options = { ...defaultOptions, ...additionalOptions };

    // Add default timestamp fields if not already defined
    const schemaWithTimestamps = {
      ...schema,
      ...AppModel.getDefaultTimestampFields(schema),
    };

    // Initialize the model
    childClass.init(schemaWithTimestamps, options);
    return childClass;
  }

  /**
   * Get default timestamp fields if not already defined in schema
   */
  static getDefaultTimestampFields(schema) {
    const timestampFields = {};

    if (!schema.created_at) {
      timestampFields.created_at = {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      };
    }

    if (!schema.updated_at) {
      timestampFields.updated_at = {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      };
    }

    return timestampFields;
  }
}

module.exports = AppModel;
