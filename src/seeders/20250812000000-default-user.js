'use strict';
const { User } = require('../models');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(_queryInterface, _Sequelize) {
    await User.create({
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'admin',
      password: 'password123',
    });
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.bulkDelete('Users', {
      email: '<EMAIL>',
    });
  },
};
