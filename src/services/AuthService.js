const jwt = require('jsonwebtoken');
const { User } = require('../models');
const AppService = require('./AppService');

class AuthService extends AppService {
  /**
   * Login user
   * @param {Object} credentials - User credentials (email, password)
   * @returns {Object} - User object and auth token
   */
  async login(credentials) {
    const { email, password } = credentials;

    // Find user by email
    const user = await User.findOne({ where: { email } });
    this.exists(user, 'User not found');

    // Check password
    const isPasswordValid = await user.verifyPassword(password);
    this.assert(isPasswordValid, 'Invalid password');

    // Generate auth token
    const jwtPayload = { userId: user.id, role: user.role };
    const jwtConfig = { expiresIn: this.config.jwtExpiresIn };
    const jwtSecret = this.config.jwtSecret;
    const authToken = jwt.sign(jwtPayload, jwtSecret, jwtConfig);

    return { user, authToken };
  }
}

module.exports = AuthService;
