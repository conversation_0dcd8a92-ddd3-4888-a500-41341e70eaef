const Ajv = require('ajv');
const addFormats = require('ajv-formats');
const InvalidError = require('../errors/InvalidError');

/**
 * Base class for all input validation classes
 * Provides common validation functionality using AJV
 */
class ApplicationInput {
  constructor(data = {}) {
    this.data = data;
    this.errors = [];
    this.isValid = false;
    this.validatedData = null;

    // Initialize AJV instance
    this.ajv = new Ajv({
      allErrors: true,
      removeAdditional: true,
      useDefaults: true,
      coerceTypes: true,
    });

    // Add format validation support (email, date, etc.)
    addFormats(this.ajv);

    // Add custom error messages
    this.ajv.addKeyword({
      keyword: 'errorMessage',
      schemaType: 'string',
      compile: () => () => true,
    });
  }

  /**
   * Get the JSON schema for validation
   * Must be implemented by child classes
   * @returns {Object} JSON schema object
   */
  schema() {
    throw new Error('schema() method must be implemented by child classes');
  }

  /**
   * Validate the input data against the schema
   * @returns {boolean} True if validation passes, false otherwise
   */
  validate() {
    const schema = this.schema();
    const validate = this.ajv.compile(schema);

    // Create a copy of data to avoid mutating original
    const dataToValidate = JSON.parse(JSON.stringify(this.data));

    this.isValid = validate(dataToValidate);

    if (this.isValid) {
      this.validatedData = dataToValidate;
      this.errors = [];
      return true;
    }

    this.errors = validate.errors;
    throw new InvalidError('Validation failed', {
      details: this.errors,
    });
  }

  /**
   * Get the validated and sanitized output data
   * Only returns data if validation has passed
   * @returns {Object|null} Validated data or null if validation failed
   */
  output() {
    if (!this.isValid || !this.validatedData) {
      throw new Error('Cannot get output: validation has not passed. Call validate() first.');
    }

    return this.validatedData;
  }

  /**
   * Get validation errors
   * @returns {Array} Array of error messages
   */
  getErrors() {
    return this.errors;
  }

  /**
   * Check if validation passed
   * @returns {boolean} True if valid, false otherwise
   */
  valid() {
    return this.isValid;
  }

  /**
   * Get the first validation error
   * @returns {string|null} First error message or null if no errors
   */
  getFirstError() {
    return this.errors.length > 0 ? this.errors[0] : null;
  }

  /**
   * Static method to create and validate input in one call
   * @param {Object} data - Input data to validate
   * @returns {ApplicationInput} Instance with validation already performed
   */
  static create(data) {
    const instance = new this(data);
    instance.validate();
    return instance;
  }
}

module.exports = ApplicationInput;
