'use strict';
const { DataTypes } = require('sequelize');
const AppModel = require('./AppModel');
const config = require('../config/config');
const bcrypt = require('bcrypt');

class User extends AppModel {
  /**
   * Associations with other models
   */
  static associate(_models) {}

  /**
   * Model schema
   */
  static schema() {
    return {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: {
            msg: 'Name cannot be empty',
          },
          len: {
            args: [2, 100],
            msg: 'Name must be between 2 and 100 characters',
          },
        },
      },
      email: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: {
          msg: 'Email address already in use',
        },
        validate: {
          isEmail: {
            msg: 'Must be a valid email address',
          },
          notEmpty: {
            msg: 'Email cannot be empty',
          },
        },
      },
      password: {
        type: DataTypes.VIRTUAL,
        allowNull: false,
        validate: {
          notEmpty: {
            msg: 'Password cannot be empty',
          },
        },
      },
      password_digest: {
        type: DataTypes.STRING,
      },
      role: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: 'user',
        validate: {
          isIn: {
            args: [['admin', 'user']],
            msg: 'Role must be admin or user',
          },
        },
      },
    };
  }

  /**
   * Lifecycle hooks
   */
  static hooks() {
    return {
      beforeCreate: async user => {
        const hashedPassword = await bcrypt.hash(user.password, config.saltRounds);
        user.password_digest = hashedPassword;
        user.created_at = new Date();
        user.updated_at = new Date();
      },
      beforeUpdate: user => {
        user.updated_at = new Date();
      },
    };
  }

  /**
   * Instance methods
   */
  async verifyPassword(password) {
    return await bcrypt.compare(password, this.password_digest);
  }
}

module.exports = User;
